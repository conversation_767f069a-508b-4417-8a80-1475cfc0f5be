'use client'
import { List } from '../../../components/skeletons'
import dayjs from 'dayjs'
import { formatDate } from '@/app/helpers/dateHelper'
import { DataTable, ExtendedColumnDef } from '@/components/filteredTable'

const CrewVoyages = ({ voyages }: { voyages?: any }) => {
    const formatDateWithTime = (dateTime: any) => {
        if (dateTime) {
            const [date, time] = dateTime.split(' ')
            const [year, month, day] = date.split('-')
            return `${day}/${month}/${year.slice(-2)} at ${time}`
        }
    }

    // Calculate sea time in hours
    const calculateSeaTime = (punchIn: any, punchOut: any) => {
        if (!punchIn || !punchOut) return '0'

        const hours = Math.floor(
            (dayjs(punchOut).valueOf() - dayjs(punchIn).valueOf()) /
                (1000 * 60 * 60),
        )

        return isNaN(hours) ? '0' : hours.toString()
    }

    // Define columns for the DataTable
    const columns: ExtendedColumnDef<any, any>[] = [
        {
            accessorKey: 'date',
            header: 'Date',
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const voyage = row.original
                return voyage.punchIn
                    ? formatDate(voyage.punchIn)
                    : formatDate(voyage.logBookEntry.startDate)
            },
        },
        {
            accessorKey: 'vessel',
            header: 'Vessel',
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const voyage = row.original
                return voyage.logBookEntry.vehicle.title
            },
        },
        {
            accessorKey: 'dutyPerformed',
            header: 'Duty performed',
            cellAlignment: 'center',
            cell: ({ row }: { row: any }) => {
                const voyage = row.original
                return voyage.dutyPerformed.title
            },
        },
        {
            accessorKey: 'signIn',
            header: 'Sign in',
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const voyage = row.original
                return formatDateWithTime(voyage.punchIn)
            },
        },
        {
            accessorKey: 'signOut',
            header: 'Sign out',
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const voyage = row.original
                return formatDateWithTime(voyage.punchOut)
            },
        },
        {
            accessorKey: 'totalSeaTime',
            header: 'Total sea time',
            cellAlignment: 'right',
            cell: ({ row }: { row: any }) => {
                const voyage = row.original
                const hours = calculateSeaTime(voyage.punchIn, voyage.punchOut)
                return `${hours} Hours`
            },
        },
    ]

    return (
        <div className="w-full p-0">
            {!voyages ? (
                <List />
            ) : (
                <DataTable
                    columns={columns}
                    data={voyages || []}
                    showToolbar={false}
                    pageSize={20}
                    showPageSizeSelector={false}
                />
            )}
        </div>
    )
}

export default CrewVoyages
